import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../pages/Public/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../pages/Auth/LoginView.vue'),
    },
  ],
})

export default router
