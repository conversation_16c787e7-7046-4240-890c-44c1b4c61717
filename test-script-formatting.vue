<template>
  <div>
    <h1>Test</h1>
  </div>
</template>

<script>
export default {
  name: 'TestComponent',
  data() {
    return {
      message: 'Hello',
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
      ],
      config: { enabled: true, settings: { theme: 'dark' } },
    }
  },
  methods: {
    handleClick() {
      console.log('clicked')
    },
    formatData(data) {
      return data.map((item) => {
        return { ...item, formatted: true }
      })
    },
  },
}
</script>
