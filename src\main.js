import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Import PrimeVue
import PrimeVue from 'primevue/config'
// Import PrimeIcons
import 'primeicons/primeicons.css'
import Aura from '@primeuix/themes/aura';
// import Lara from '@primeuix/themes/lara';

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
// Use PrimeVue
app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            darkModeSelector: false
        }
    }
});

app.mount('#app')
